from django.shortcuts import render, redirect, get_object_or_404
from django.conf import settings
from django.core.mail import send_mail
from django.contrib import messages
from .models import Category, Product, ProductTranslation, CarouselItem, CarouselTranslation, HomeLayoutBlock, NavigationItem
from .forms import ContactForm


def get_locale(request):
    loc = request.session.get('locale')
    if loc not in ('zh','en'):
        loc = 'zh'
    return loc


def load_nav(locale: str):
    items = NavigationItem.objects.filter(is_active=True).order_by('sort_order')
    nav = []
    for it in items:
        t = it.translations.filter(locale=locale).first()
        label = t.label if t else it.label_key
        url = it.target
        if it.type == 'category':
            url = f"/products/?cat={it.target}"
        # ensure built-in pages route correctly even if seed data target differs
        if it.label_key == 'about':
            url = '/about/'
        if it.label_key == 'products':
            url = '/products/'
        if it.label_key == 'contact':
            url = '/contact/'
        nav.append({"label": label, "url": url})
    return nav


def home(request):
    locale = get_locale(request)
    blocks = HomeLayoutBlock.objects.filter(is_active=True).order_by('sort_order')

    # Prepare datasets; will render per blocks
    carousels = []
    cats = []
    featured_products = []
    stories = []
    testimonials = []

    if blocks.filter(block_type='carousel').exists():
        items = CarouselItem.objects.filter(is_active=True).order_by('sort_order')
        for item in items:
            t = item.translations.filter(locale=locale).first()
            carousels.append({
                'image': item.image.url if item.image else '',
                'title': t.title if t else '',
                'subtitle': t.subtitle if t else '',
                'cta_text': t.cta_text if t else '',
                'link': item.link or '#'
            })

    if blocks.filter(block_type='categories').exists():
        cats = list(Category.objects.filter(is_active=True).order_by('sort_order'))

    if blocks.filter(block_type='products').exists():
        cfg = blocks.filter(block_type='products').first().config or {}
        tag = cfg.get('tag', 'featured')
        limit = int(cfg.get('limit', 8))
        qs = Product.objects.filter(is_active=True)
        if tag == 'featured':
            qs = qs.filter(tag_featured=True)
        elif tag == 'new':
            qs = qs.filter(tag_new=True)
        elif tag == 'hot':
            qs = qs.filter(tag_hot=True)
        for p in qs.order_by('sort_order')[:limit]:
            t = p.translations.filter(locale=locale).first()
            featured_products.append({
                'id': p.id,
                'name': t.name if t else f'Product {p.id}',
                'short_desc': t.short_desc if t else '',
                'cover': p.cover_image.url if p.cover_image else '',
                'tags': [tag for tag,flag in [('new',p.tag_new),('hot',p.tag_hot),('featured',p.tag_featured)] if flag]
            })

    if blocks.filter(block_type='story').exists():
        from .models import Post
        posts = Post.objects.filter(status='published').order_by('-published_at')[:2]
        for s in posts:
            tt = s.translations.filter(locale=locale).first()
            title = tt.title if tt else 'Story'
            content = tt.content if tt else ''
            excerpt = content[:80]
            stories.append({'title': title, 'excerpt': excerpt})

    if blocks.filter(block_type='testimonials').exists():
        from .models import Testimonial
        ts = Testimonial.objects.filter(is_active=True).order_by('sort_order')[:6]
        for t in ts:
            tt = t.translations.filter(locale=locale).first()
            testimonials.append({'author': (tt.author_name if tt else ''), 'content': (tt.content if tt else '')})

    context = {
        'locale': locale,
        'nav': load_nav(locale),
        'carousels': carousels,
        'categories': cats,
        'featured_products': featured_products,
        'stories': stories,
        'testimonials': testimonials,
        'blocks': blocks,
        'slogan': 'Happy Pets Happy Owner',
        'breadcrumb': [],
    }
    return render(request, 'site/home.html', context)


def product_list(request):
    locale = get_locale(request)
    qs = Product.objects.filter(is_active=True)
    cat = request.GET.get('cat')
    if cat:
        qs = qs.filter(category__slug=cat)
    products = qs.order_by('sort_order')

    items = []
    for p in products:
        t = p.translations.filter(locale=locale).first()
        items.append({
            'id': p.id,
            'name': t.name if t else f'Product {p.id}',
            'short_desc': t.short_desc if t else '',
            'cover': p.cover_image.url if p.cover_image else '',
            'tags': [tag for tag,flag in [('new',p.tag_new),('hot',p.tag_hot),('featured',p.tag_featured)] if flag],
            'usage': p.usage,
        })

    context = {
        'items': items,
        'nav': load_nav(locale),
        'locale': locale,
        'slogan':'Happy Pets Happy Owner',
        'cat': cat,
        'breadcrumb': [{'label': ('Products' if locale=='en' else '产品'), 'url': '/products/'}] +
                      ([{'label': cat, 'url': None}] if cat else []),
    }
    return render(request, 'site/product_list.html', context)


def product_detail(request, pk: int):
    locale = get_locale(request)
    p = get_object_or_404(Product, pk=pk, is_active=True)
    t = p.translations.filter(locale=locale).first()
    data = {
        'id': p.id,
        'name': t.name if t else f'Product {p.id}',
        'short_desc': t.short_desc if t else '',
        'rich_desc': t.rich_desc if t else '',
        'cover': p.cover_image.url if p.cover_image else '',
        'gallery': [g.image.url for g in p.gallery.order_by('sort_order')],
        'tags': [tag for tag,flag in [('new',p.tag_new),('hot',p.tag_hot),('featured',p.tag_featured)] if flag]
    }
    bc = [{'label': ('Products' if locale=='en' else '产品'), 'url': '/products/'}]
    bc.append({'label': data['name'], 'url': None})
    return render(request, 'site/product_detail.html', {'product': data, 'nav': load_nav(locale), 'locale': locale, 'slogan':'Happy Pets Happy Owner', 'breadcrumb': bc})


def contact(request):
    locale = get_locale(request)
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            name = form.cleaned_data['name']
            email = form.cleaned_data['email']
            message = form.cleaned_data['message']
            subject = f"[Pecco Contact] {name}"
            body = f"From: {name} <{email}>\n\n{message}"
            send_mail(subject, body, settings.DEFAULT_FROM_EMAIL, [settings.CONTACT_RECIPIENT_EMAIL])
            messages.success(request, '已发送，我们会尽快与您联系。' if locale=='zh' else 'Sent! We will get back to you soon.')
            return redirect('home')
    else:
        form = ContactForm()
    return render(request, 'site/contact.html', {'form': form, 'nav': load_nav(locale), 'locale': locale, 'slogan':'Happy Pets Happy Owner', 'breadcrumb':[{'label': ('Contact' if locale=='en' else '联系我们'), 'url': None}]})


def about(request):
    locale = get_locale(request)
    bc = [{'label': ('About Us' if locale=='en' else '关于我们'), 'url': None}]
    return render(request, 'site/about.html', {
        'nav': load_nav(locale),
        'locale': locale,
        'slogan': 'Happy Pets Happy Owner',
        'breadcrumb': bc,
    })


def switch_lang(request, locale: str):
    if locale not in ('zh','en'):
        locale = 'zh'
    request.session['locale'] = locale
    return redirect('home')

