{% load static %}
<!doctype html>
{% if locale == 'zh' %}<html lang="zh">{% else %}<html lang="en">{% endif %}
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>pecco - Happy Pets Happy Owner</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="{% static 'css/site.css' %}">
</head>
<body>
<header class="topbar">
  <div class="left">
    <a href="/"><img src="{% static 'img/logo_pecco.svg' %}" class="logo" alt="pecco logo"/></a>
  </div>
  <nav class="main-nav">
    {% for n in nav %}
      <a href="{{ n.url }}">{{ n.label }}</a>
    {% endfor %}
  </nav>
  <div class="right">
    <a href="/lang/zh" class="lang{% if locale == 'zh' %} active{% endif %}">中文</a>
    <span class="divider">/</span>
    <a href="/lang/en" class="lang{% if locale == 'en' %} active{% endif %}">EN</a>
  </div>
</header>
<div class="tagline-bar">
  <div class="tagline">{{ slogan }}</div>
</div>
{% if messages %}
<div class="flash-area">
  {% for m in messages %}
    <div class="flash {{ m.tags }}">{{ m }}</div>
  {% endfor %}
</div>
{% endif %}
{% if breadcrumb %}
  <div class="breadcrumb">
    <a href="/">{% if locale == 'zh' %}首页{% else %}Home{% endif %}</a>
    {% for bc in breadcrumb %}
      <span class="sep">›</span>
      {% if bc.url %}<a href="{{ bc.url }}">{{ bc.label }}</a>{% else %}<span>{{ bc.label }}</span>{% endif %}
    {% endfor %}
  </div>
{% endif %}
<main class="page">
  {% block content %}{% endblock %}
</main>
<footer class="footer">
  <p>© {% now 'Y' %} pecco. All rights reserved.</p>
</footer>
<script src="{% static 'js/site.js' %}"></script>
</body>
</html>

