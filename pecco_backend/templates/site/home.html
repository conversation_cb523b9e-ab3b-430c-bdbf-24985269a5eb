{% extends 'site/base.html' %}
{% load static %}
{% block content %}
{% for b in blocks %}
  {% if b.block_type == 'carousel' %}
  <section class="hero">
    <div class="carousel">
      {% for c in carousels %}
      <div class="slide">
        <img src="{{ c.image }}" alt="carousel">
        <div class="overlay">
          <h2>{{ c.title }}</h2>
          <p>{{ c.subtitle }}</p>
          {% if c.cta_text %}<a class="btn" href="{{ c.link }}">{{ c.cta_text }}</a>{% endif %}
        </div>
      </div>
      {% empty %}
        <div class="slide placeholder">欢迎来到 pecco</div>
      {% endfor %}
    </div>
  </section>
  {% elif b.block_type == 'categories' %}
  <section class="categories">
    <div class="section-header">
      <h2 class="section-title">{% if locale == 'zh' %}按宠物分类{% else %}Shop by pets type{% endif %}</h2>
      <div class="section-action"><a href="/products/">{% if locale == 'zh' %}查看全部{% else %}View All{% endif %}</a></div>
    </div>
    <div class="grid">
    {% for cat in categories %}
      <a class="category-card" href="/products/?cat={{ cat.slug }}">
        {% if cat.icon %}
          <img src="{{ cat.icon.url }}" alt="{{ cat.slug }}" class="avatar">
        {% elif cat.slug == 'dog' %}
          <img src="{% static 'img/dog_avatar.svg' %}" class="avatar" alt="dog"/>
        {% elif cat.slug == 'cat' %}
          <img src="{% static 'img/cat_avatar.svg' %}" class="avatar" alt="cat"/>
        {% else %}
          <img src="{% static 'img/pet_avatar_placeholder.png' %}" class="avatar" alt="pet"/>
        {% endif %}
        <div class="name">{{ cat.slug|title }}</div>
      </a>
    {% empty %}
      <p>{% if locale == 'zh' %}暂无分类{% else %}No categories available{% endif %}</p>
    {% endfor %}
    </div>
  </section>
  {% elif b.block_type == 'products' %}
  <section class="product-list">
    <h3>{% if locale == 'zh' %}精选产品{% else %}Featured Products{% endif %}</h3>
    <div class="grid">
      {% for p in featured_products %}
        <a class="product-card" href="/products/{{ p.id }}/">
          <img src="{{ p.cover }}" alt="{{ p.name }}">
          <h4>{{ p.name }}</h4>
          <p>{{ p.short_desc }}</p>
          <div class="tags">
            {% for t in p.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
          </div>
        </a>
      {% empty %}
        <p>{% if locale == 'zh' %}暂无产品{% else %}No products{% endif %}</p>
      {% endfor %}
    </div>
  </section>
  {% elif b.block_type == 'story' %}
  <section class="stories">
    <h3>{% if locale == 'zh' %}品牌故事{% else %}Brand Story{% endif %}</h3>
    <div class="story-cards">
      {% for s in stories %}
      <article class="product-card">
        <h4>{{ s.title }}</h4>
        <p>{{ s.excerpt }}</p>
      </article>
      {% empty %}
        <p>{% if locale == 'zh' %}暂无内容{% else %}No content{% endif %}</p>
      {% endfor %}
    </div>
  </section>
  {% elif b.block_type == 'testimonials' %}
  <section class="testimonials">
    <h3>{% if locale == 'zh' %}客户评价{% else %}Customer Reviews{% endif %}</h3>
    <div class="grid">
      {% for t in testimonials %}
      <div class="product-card">
        <p>“{{ t.content }}”</p>
        <div style="margin-top:12px;color:var(--color-text-light);font-weight:500;">— {{ t.author }}</div>
      </div>
      {% empty %}
        <p>{% if locale == 'zh' %}暂无评价{% else %}No testimonials{% endif %}</p>
      {% endfor %}
    </div>
  </section>
  {% endif %}
{% endfor %}
{% endblock %}

