/* CSS Variables for consistent styling */
:root {
  --font-family-primary: 'Jo<PERSON>', -apple-system, BlinkMacSystemFont, 'Inter', 'Noto Sans SC', sans-serif;
  --color-text: #232323;
  --color-text-light: #3c3c3c;
  --color-background: #ffffff;
  --color-background-light: #f8f8f8;
  --color-accent: #051c42;
  --color-accent-hover: #234bbb;
  --spacing-section: 80px;
  --spacing-large: 60px;
  --spacing-medium: 40px;
  --spacing-small: 24px;
  --container-max-width: 1400px;
  --container-padding: 40px;
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  background: var(--color-background);
  color: var(--color-text);
  font-weight: 400;
  line-height: 1.6;
}

.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px var(--container-padding);
  background: rgba(255,255,255,.95);
  backdrop-filter: saturate(180%) blur(10px);
  position: sticky;
  top: 0;
  border-bottom: 1px solid #e6e6e6;
  z-index: 100;
}

.topbar .left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo {
  width: 100px;
  height: 42px;
  transition: all .3s ease;
  filter: drop-shadow(0 2px 8px rgba(139,146,153,.15));
}

.logo:hover {
  transform: translateY(-1px);
  filter: drop-shadow(0 4px 12px rgba(139,146,153,.25));
}

.tagline-bar {
  background: var(--color-background-light);
  border-bottom: 1px solid #e6e6e6;
}

.tagline {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 16px var(--container-padding);
  color: var(--color-text-light);
  font-weight: 500;
  text-align: center;
  font-size: 16px;
}

main.page {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.breadcrumb {
  max-width: var(--container-max-width);
  margin: 12px auto 0;
  padding: 0 var(--container-padding);
  color: #999999;
  font-size: 14px;
}

.breadcrumb a {
  color: var(--color-accent);
  text-decoration: none;
}

.breadcrumb .sep {
  margin: 0 8px;
  color: #d3d3d3;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-nav a {
  margin: 0;
  color: var(--color-text);
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 30px;
  transition: all .3s ease;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
}

.main-nav a:hover {
  background: var(--color-background-light);
  color: var(--color-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(15,23,42,.08);
}

.right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.right .lang {
  color: var(--color-text-light);
  text-decoration: none;
  margin: 0 8px;
  font-weight: 500;
  transition: color .3s ease;
}

.right .lang.active {
  color: var(--color-accent);
}

.right .divider {
  color: #d3d3d3;
  margin: 0 4px;
}
/* Hero Section */
.hero {
  margin: var(--spacing-medium) 0 var(--spacing-section);
}

.hero .carousel {
  display: grid;
  grid-auto-rows: 320px;
}

.hero .slide {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  margin: 0;
  background: linear-gradient(180deg, rgba(15,23,42,.12), rgba(15,23,42,.04));
  box-shadow: 0 20px 50px rgba(15,23,42,.10);
}

.hero .slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.hero .slide .overlay {
  position: absolute;
  left: 60px;
  bottom: 60px;
  color: #fff;
  text-shadow: 0 4px 12px rgba(0,0,0,.4);
}

.hero .slide .overlay h2 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px;
  line-height: 1.2;
}

.hero .slide .overlay p {
  font-size: 18px;
  font-weight: 400;
  margin: 0 0 24px;
  opacity: 0.95;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 16px 32px;
  border-radius: 30px;
  background: var(--color-accent);
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.02em;
  transition: all .3s ease;
  border: 2px solid var(--color-accent);
}

.btn:hover {
  background: var(--color-accent-hover);
  border-color: var(--color-accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(5,28,66,.20);
}
/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 0 var(--spacing-medium);
  padding: 0;
}

.section-title {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.3;
  letter-spacing: -0.02em;
  margin: 0;
}

.section-action a {
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all .3s ease;
}

.section-action a:hover {
  background: var(--color-background-light);
  color: var(--color-accent-hover);
}
/* Categories Section */
.categories {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
}

.categories .grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 250px));
  gap: var(--spacing-medium);
  justify-content: center;
  justify-items: center;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-medium) var(--spacing-small);
  border-radius: 20px;
  background: var(--color-background);
  text-decoration: none;
  color: var(--color-text);
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  transition: all .3s ease;
  border: 1px solid #f0f0f0;
  min-height: 280px;
  justify-content: center;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(15,23,42,.12);
  border-color: #e0e0e0;
}

.category-card .avatar {
  width: 140px;
  height: 140px;
  border-radius: 50%;
  box-shadow: 0 8px 20px rgba(15,23,42,.08);
  background: var(--color-background);
  object-fit: cover;
  border: 3px solid #f8f8f8;
}

.category-card .name {
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  text-align: center;
  color: var(--color-text);
}
/* Product List Section */
.product-list {
  padding: var(--spacing-section) 0;
  background: var(--color-background-light);
  margin: var(--spacing-medium) 0;
  border-radius: 30px;
}

.product-list h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: left;
}

.product-list .grid,
.testimonials .grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-medium);
}

.product-card {
  display: block;
  padding: var(--spacing-small);
  border-radius: 20px;
  background: var(--color-background);
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  color: var(--color-text);
  text-decoration: none;
  transition: all .3s ease;
  border: 1px solid #f0f0f0;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(15,23,42,.12);
  border-color: #e0e0e0;
}

.product-card img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: 16px;
  transition: transform .3s ease;
}

.product-card:hover img {
  transform: scale(1.03);
}

.product-card h4 {
  margin: 16px 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  line-height: 1.4;
}

.product-card p {
  margin: 0 0 12px;
  color: var(--color-text-light);
  font-size: 14px;
  line-height: 1.5;
}

.product-card .tags {
  margin-top: 12px;
}

.product-card .tag {
  display: inline-block;
  margin-right: 8px;
  margin-top: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: var(--color-background-light);
  color: var(--color-text-light);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-card .tag-featured {
  background: var(--color-accent);
  color: #fff;
}
/* Stories Section */
.stories {
  padding: var(--spacing-section) 0 !important;
  background: var(--color-background);
}

.stories h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: left;
}

.story-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-medium);
}

/* Testimonials Section */
.testimonials {
  padding: var(--spacing-section) 0 !important;
  background: var(--color-background-light);
  margin: var(--spacing-medium) 0;
  border-radius: 30px;
}

.testimonials h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: left;
}

.testimonials .product-card {
  padding: var(--spacing-medium);
}

.testimonials .product-card p {
  font-style: italic;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  color: var(--color-text-light);
}

/* Product Detail */
.product-detail {
  display: grid;
  grid-template-columns: 1.2fr .8fr;
  gap: var(--spacing-large);
  padding: var(--spacing-section) 0;
}

.product-detail .gallery img {
  width: 100%;
  border-radius: 20px;
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
}

/* Flash Messages */
.flash-area {
  position: fixed;
  top: 90px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 999;
}

.flash {
  background: #e6ffef;
  color: #147d3f;
  border: 1px solid #b7f0cc;
  padding: 16px 24px;
  border-radius: 20px;
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  font-weight: 500;
}

/* Footer */
.footer {
  padding: var(--spacing-large) var(--container-padding);
  color: var(--color-text-light);
  text-align: center;
  background: var(--color-background-light);
  border-top: 1px solid #e6e6e6;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 900px) {
  :root {
    --container-padding: 20px;
    --spacing-section: 60px;
    --spacing-large: 40px;
    --spacing-medium: 30px;
  }

  .product-detail {
    grid-template-columns: 1fr;
  }

  .hero .slide .overlay {
    left: 30px;
    bottom: 30px;
  }

  .hero .slide .overlay h2 {
    font-size: 24px;
  }

  .hero .slide .overlay p {
    font-size: 16px;
  }

  .section-title {
    font-size: 24px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
}
