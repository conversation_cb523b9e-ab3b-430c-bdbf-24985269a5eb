<svg width="120" height="42" viewBox="0 0 120 42" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Subtle background with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.08);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.02);stop-opacity:1" />
    </linearGradient>
  </defs>

  <rect width="120" height="42" rx="12" fill="url(#bgGradient)" stroke="rgba(139,146,153,0.2)" stroke-width="1"/>

  <!-- pecco text logo with better positioning -->
  <text x="60" y="28"
        font-family="Jost, -apple-system, BlinkMacSystemFont, sans-serif"
        font-size="18"
        font-weight="700"
        text-anchor="middle"
        fill="#8B9299"
        letter-spacing="0.8px"
        dominant-baseline="middle">
    pecco
  </text>

  <!-- Elegant decorative element -->
  <g opacity="0.6">
    <circle cx="104" cy="21" r="1.2" fill="#8B9299"/>
    <circle cx="108" cy="18" r="0.8" fill="#8B9299" opacity="0.7"/>
    <circle cx="111" cy="24" r="0.6" fill="#8B9299" opacity="0.5"/>
  </g>
</svg>
